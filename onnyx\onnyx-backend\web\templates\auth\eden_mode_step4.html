{% extends "base.html" %}

{% block title %}Eden Mode Activation - ONNYX{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-onyx-black via-onyx-dark to-onyx-black relative overflow-hidden">
    <!-- Standardized Background Effects -->
    <div class="absolute inset-0 overflow-hidden">
        <!-- Professional cyber grid background -->
        <div class="absolute inset-0 cyber-grid opacity-20"></div>
        <!-- Floating particles effect - Enhanced visibility -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-purple rounded-full animate-ping"></div>
            <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-cyan rounded-full animate-pulse"></div>
            <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce"></div>
            <div class="absolute top-2/3 right-1/4 w-1 h-1 bg-cyber-purple rounded-full animate-ping"></div>
            <div class="absolute top-1/2 left-1/2 w-1 h-1 bg-cyber-cyan rounded-full animate-pulse"></div>
            <div class="absolute bottom-1/3 right-1/2 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce"></div>
            <div class="absolute top-3/4 left-1/5 w-1 h-1 bg-cyber-purple rounded-full animate-ping"></div>
            <div class="absolute bottom-1/5 right-1/5 w-1.5 h-1.5 bg-cyber-cyan rounded-full animate-pulse"></div>
        </div>
    </div>

    <!-- Progress Indicator - Non-sticky -->
    <div class="flex justify-center mb-8">
        <div class="glass-card-enhanced px-8 py-4 rounded-2xl">
            <div class="flex items-center space-x-4">
                <div class="w-8 h-8 rounded-full bg-cyber-green flex items-center justify-center text-onyx-black font-bold">✓</div>
                <div class="w-8 h-1 bg-cyber-green"></div>
                <div class="w-8 h-8 rounded-full bg-cyber-green flex items-center justify-center text-onyx-black font-bold">✓</div>
                <div class="w-8 h-1 bg-cyber-green"></div>
                <div class="w-8 h-8 rounded-full bg-cyber-green flex items-center justify-center text-onyx-black font-bold">✓</div>
                <div class="w-8 h-1 bg-cyber-cyan"></div>
                <div class="w-8 h-8 rounded-full bg-cyber-cyan flex items-center justify-center text-onyx-black font-bold">4</div>
                <div class="w-8 h-1 bg-glass-border"></div>
                <div class="w-8 h-8 rounded-full bg-glass-border flex items-center justify-center text-text-tertiary">5</div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container-xl mx-auto px-6 pt-32 pb-16">
        <div class="max-w-5xl mx-auto">

            <!-- Header -->
            <div class="text-center mb-16 fade-in-up">
                <div class="mb-8">
                    <div class="inline-block glass-card-premium p-6 rounded-3xl mb-6">
                        <svg class="w-16 h-16 text-cyber-purple mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <h1 class="text-6xl md:text-7xl font-orbitron font-bold text-cyber-purple mb-6 glow-text">
                        Eden Mode Activation
                    </h1>
                    <p class="text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed">
                        Your covenant identity awakens. Now choose your path to
                        <span class="text-cyber-cyan font-semibold">economic sovereignty</span>
                        within the biblical marketplace.
                    </p>
                </div>
            </div>

            <!-- Journey Summary -->
            <div class="glass-card-premium p-8 rounded-3xl mb-12 fade-in-up" data-delay="300">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6">
                        🌟 Your Covenant Journey
                    </h2>
                </div>

                <div id="journeySummary" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Dynamic content will be inserted here -->
                </div>
            </div>

            <!-- Sela Path Selection -->
            <div class="space-y-12">
                <!-- Path Introduction -->
                <div class="glass-card-premium p-8 rounded-3xl fade-in-up" data-delay="600">
                    <div class="text-center mb-8">
                        <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6">
                            🏢 Choose Your Economic Path
                        </h2>
                        <p class="text-lg text-text-secondary max-w-3xl mx-auto mb-6">
                            "And he gave some, apostles; and some, prophets; and some, evangelists;
                            and some, pastors and teachers; For the perfecting of the saints,
                            for the work of the ministry, for the edifying of the body of Christ"
                        </p>
                        <p class="text-cyber-purple font-orbitron">— Ephesians 4:11-12</p>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Create New Sela -->
                        <div class="sela-path glass-card-enhanced p-6 rounded-2xl cursor-pointer transition-all duration-300 hover:scale-105"
                             data-path="create">
                            <div class="text-center mb-6">
                                <div class="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-cyber-cyan/20 to-cyber-purple/20 flex items-center justify-center text-4xl">
                                    🏗️
                                </div>
                                <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-3">
                                    Create New Sela Business
                                </h3>
                                <p class="text-text-secondary leading-relaxed text-sm mb-4">
                                    Establish your own covenant business validator. Lead others in biblical economics and earn mining rewards.
                                </p>
                            </div>

                            <div class="space-y-3">
                                <div class="flex items-center space-x-2">
                                    <span class="text-cyber-green text-sm">✓</span>
                                    <span class="text-text-secondary text-sm">Become a blockchain validator</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-cyber-green text-sm">✓</span>
                                    <span class="text-text-secondary text-sm">Earn mining rewards</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-cyber-green text-sm">✓</span>
                                    <span class="text-text-secondary text-sm">Lead covenant community</span>
                                </div>
                            </div>

                            <!-- Selection Indicator -->
                            <div class="selection-indicator mt-4 text-center opacity-0 transition-opacity duration-300">
                                <div class="w-6 h-6 mx-auto rounded-full bg-cyber-green flex items-center justify-center">
                                    <svg class="w-4 h-4 text-onyx-black" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <p class="text-cyber-green font-semibold mt-1 text-sm">Selected</p>
                            </div>
                        </div>

                        <!-- Join Existing Sela -->
                        <div class="sela-path glass-card-enhanced p-6 rounded-2xl cursor-pointer transition-all duration-300 hover:scale-105"
                             data-path="join">
                            <div class="text-center mb-6">
                                <div class="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-cyber-purple/20 to-cyber-green/20 flex items-center justify-center text-4xl">
                                    🤝
                                </div>
                                <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-3">
                                    Join Existing Sela
                                </h3>
                                <p class="text-text-secondary leading-relaxed text-sm mb-4">
                                    Connect with an established covenant business and contribute your labor to their mission.
                                </p>
                            </div>

                            <div class="space-y-3">
                                <div class="flex items-center space-x-2">
                                    <span class="text-cyber-green text-sm">✓</span>
                                    <span class="text-text-secondary text-sm">Immediate participation</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-cyber-green text-sm">✓</span>
                                    <span class="text-text-secondary text-sm">Learn from leaders</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-cyber-green text-sm">✓</span>
                                    <span class="text-text-secondary text-sm">Share in prosperity</span>
                                </div>
                            </div>

                            <!-- Selection Indicator -->
                            <div class="selection-indicator mt-4 text-center opacity-0 transition-opacity duration-300">
                                <div class="w-6 h-6 mx-auto rounded-full bg-cyber-green flex items-center justify-center">
                                    <svg class="w-4 h-4 text-onyx-black" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <p class="text-cyber-green font-semibold mt-1 text-sm">Selected</p>
                            </div>
                        </div>

                        <!-- Community Member -->
                        <div class="sela-path glass-card-enhanced p-6 rounded-2xl cursor-pointer transition-all duration-300 hover:scale-105"
                             data-path="community">
                            <div class="text-center mb-6">
                                <div class="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-cyber-green/20 to-cyber-blue/20 flex items-center justify-center text-4xl">
                                    🌟
                                </div>
                                <h3 class="text-xl font-orbitron font-bold text-cyber-green mb-3">
                                    Community Member
                                </h3>
                                <p class="text-text-secondary leading-relaxed text-sm mb-4">
                                    Join as an independent covenant community member. Participate in governance and biblical economics.
                                </p>
                            </div>

                            <div class="space-y-3">
                                <div class="flex items-center space-x-2">
                                    <span class="text-cyber-green text-sm">✓</span>
                                    <span class="text-text-secondary text-sm">Participate in governance</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-cyber-green text-sm">✓</span>
                                    <span class="text-text-secondary text-sm">Access biblical tokenomics</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-cyber-green text-sm">✓</span>
                                    <span class="text-text-secondary text-sm">Independent participation</span>
                                </div>
                            </div>

                            <!-- Selection Indicator -->
                            <div class="selection-indicator mt-4 text-center opacity-0 transition-opacity duration-300">
                                <div class="w-6 h-6 mx-auto rounded-full bg-cyber-green flex items-center justify-center">
                                    <svg class="w-4 h-4 text-onyx-black" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <p class="text-cyber-green font-semibold mt-1 text-sm">Selected</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Create Sela Form -->
                <div id="createSelaForm" class="glass-card-premium p-8 rounded-3xl hidden fade-in-up">
                    <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-8 text-center">
                        🏗️ Establish Your Covenant Business
                    </h2>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div class="space-y-6">
                            <div>
                                <label for="businessName" class="block text-lg font-semibold text-cyber-cyan mb-3">
                                    Business Name *
                                </label>
                                <input type="text"
                                       id="businessName"
                                       name="businessName"
                                       placeholder="Enter your covenant business name"
                                       class="w-full h-14 px-6 glass-card border border-glass-border rounded-xl text-white placeholder-text-tertiary focus:border-cyber-cyan focus:ring-2 focus:ring-cyber-cyan/20 transition-all duration-300 font-orbitron">
                                <p class="text-text-tertiary text-sm mt-2">
                                    This will be your Sela validator identity
                                </p>
                            </div>

                            <div>
                                <label for="businessType" class="block text-lg font-semibold text-cyber-cyan mb-3">
                                    Business Category *
                                </label>
                                <select id="businessType"
                                        name="businessType"
                                        class="w-full h-14 px-6 glass-card border border-glass-border rounded-xl text-white focus:border-cyber-cyan focus:ring-2 focus:ring-cyber-cyan/20 transition-all duration-300 font-orbitron">
                                    <option value="">Select business category</option>
                                    <option value="spiritual">Spiritual Services & Ministry</option>
                                    <option value="governance">Leadership & Governance</option>
                                    <option value="judicial">Justice & Mediation</option>
                                    <option value="security">Protection & Security</option>
                                    <option value="temporal">Timing & Coordination</option>
                                    <option value="technology">Technology & Innovation</option>
                                    <option value="agriculture">Agriculture & Food Production</option>
                                    <option value="construction">Construction & Building</option>
                                    <option value="education">Education & Training</option>
                                    <option value="healthcare">Healthcare & Wellness</option>
                                    <option value="finance">Financial Services (Biblical)</option>
                                    <option value="creative">Creative & Media</option>
                                    <option value="trade">Trade & Commerce</option>
                                    <option value="manufacturing">Manufacturing & Crafts</option>
                                    <option value="cultural">Cultural Heritage Services</option>
                                </select>
                                <div id="tribalCallingNotice" class="hidden mt-3 p-3 bg-cyber-cyan/10 rounded-lg border border-cyber-cyan/30">
                                    <div class="flex items-start space-x-3">
                                        <div class="text-cyber-cyan text-lg">⚖️</div>
                                        <div>
                                            <p class="text-cyber-cyan font-semibold text-sm">Tribal Calling Detected</p>
                                            <p class="text-text-secondary text-xs mt-1" id="tribalCallingMessage"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label for="businessDescription" class="block text-lg font-semibold text-cyber-cyan mb-3">
                                    Business Description *
                                </label>
                                <textarea id="businessDescription"
                                          name="businessDescription"
                                          rows="4"
                                          placeholder="Describe how your business will serve the covenant community..."
                                          class="w-full px-6 py-4 glass-card border border-glass-border rounded-xl text-white placeholder-text-tertiary focus:border-cyber-cyan focus:ring-2 focus:ring-cyber-cyan/20 transition-all duration-300 font-orbitron resize-none"></textarea>
                            </div>
                        </div>

                        <div class="space-y-6">
                            <div class="glass-card-enhanced p-6 rounded-xl">
                                <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-4">
                                    🏛️ Sela Validator Benefits
                                </h3>
                                <div class="space-y-3 text-text-secondary">
                                    <p class="flex items-center space-x-3">
                                        <span class="text-cyber-green">✓</span>
                                        <span>Validate blockchain transactions</span>
                                    </p>
                                    <p class="flex items-center space-x-3">
                                        <span class="text-cyber-green">✓</span>
                                        <span>Earn mining rewards (up to 1000 ONX/block)</span>
                                    </p>
                                    <p class="flex items-center space-x-3">
                                        <span class="text-cyber-green">✓</span>
                                        <span>Participate in governance decisions</span>
                                    </p>
                                    <p class="flex items-center space-x-3">
                                        <span class="text-cyber-green">✓</span>
                                        <span>Lead covenant community</span>
                                    </p>
                                    <p class="flex items-center space-x-3">
                                        <span class="text-cyber-green">✓</span>
                                        <span>Access to biblical lending pools</span>
                                    </p>
                                </div>
                            </div>

                            <div class="glass-card-enhanced p-6 rounded-xl">
                                <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-4">
                                    📊 Economic Transparency
                                </h3>
                                <div class="space-y-4">
                                    <div class="flex justify-between items-center">
                                        <span class="text-text-secondary">Mining Rewards</span>
                                        <span class="text-cyber-cyan font-bold">85%</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-text-secondary">Worker Profit Sharing</span>
                                        <span class="text-cyber-green font-bold">10%</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-text-secondary">Covenant Fund</span>
                                        <span class="text-cyber-purple font-bold">5%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Join Sela Options -->
                <div id="joinSelaOptions" class="glass-card-premium p-8 rounded-3xl hidden fade-in-up">
                    <h2 class="text-3xl font-orbitron font-bold text-cyber-purple mb-8 text-center">
                        🤝 Join Covenant Community
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="availableSelas">
                        <!-- Available Selas will be loaded here -->
                    </div>

                    <div class="text-center mt-8">
                        <p class="text-text-secondary mb-4">
                            Don't see a Sela that matches your calling?
                        </p>
                        <button id="switchToCreate"
                                class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                            Create Your Own Sela Instead
                        </button>
                    </div>
                </div>

                <!-- Community Member Confirmation -->
                <div id="communityMemberConfirmation" class="glass-card-premium p-8 rounded-3xl hidden fade-in-up">
                    <h2 class="text-3xl font-orbitron font-bold text-cyber-green mb-8 text-center">
                        🌟 Welcome to the Covenant Community
                    </h2>

                    <div class="text-center mb-8">
                        <div class="w-24 h-24 mx-auto mb-6 rounded-3xl bg-gradient-to-br from-cyber-green/20 to-cyber-blue/20 flex items-center justify-center text-6xl">
                            🌟
                        </div>
                        <p class="text-lg text-text-secondary max-w-2xl mx-auto leading-relaxed mb-6">
                            You have chosen to join as an independent covenant community member.
                            You will have access to all community features, governance participation,
                            and biblical tokenomics without business obligations.
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                        <div class="glass-card-enhanced p-6 rounded-xl">
                            <h3 class="text-xl font-orbitron font-bold text-cyber-green mb-4">
                                🗳️ Community Benefits
                            </h3>
                            <div class="space-y-3 text-text-secondary">
                                <p class="flex items-center space-x-3">
                                    <span class="text-cyber-green">✓</span>
                                    <span>Participate in governance voting</span>
                                </p>
                                <p class="flex items-center space-x-3">
                                    <span class="text-cyber-green">✓</span>
                                    <span>Access biblical tokenomics features</span>
                                </p>
                                <p class="flex items-center space-x-3">
                                    <span class="text-cyber-green">✓</span>
                                    <span>Join community discussions</span>
                                </p>
                                <p class="flex items-center space-x-3">
                                    <span class="text-cyber-green">✓</span>
                                    <span>Participate in Sabbath and Jubilee cycles</span>
                                </p>
                            </div>
                        </div>

                        <div class="glass-card-enhanced p-6 rounded-xl">
                            <h3 class="text-xl font-orbitron font-bold text-cyber-blue mb-4">
                                🔮 Future Opportunities
                            </h3>
                            <div class="space-y-3 text-text-secondary">
                                <p class="flex items-center space-x-3">
                                    <span class="text-cyber-blue">→</span>
                                    <span>Create a Sela business later</span>
                                </p>
                                <p class="flex items-center space-x-3">
                                    <span class="text-cyber-blue">→</span>
                                    <span>Join existing Sela businesses</span>
                                </p>
                                <p class="flex items-center space-x-3">
                                    <span class="text-cyber-blue">→</span>
                                    <span>Upgrade to validator status</span>
                                </p>
                                <p class="flex items-center space-x-3">
                                    <span class="text-cyber-blue">→</span>
                                    <span>Access advanced features</span>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <div class="glass-card-enhanced p-6 rounded-xl bg-gradient-to-r from-cyber-green/10 to-cyber-blue/10">
                            <p class="text-cyber-green font-orbitron font-semibold mb-2">
                                "For where two or three gather in my name, there am I with them."
                            </p>
                            <p class="text-text-secondary text-sm">— Matthew 18:20</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="flex justify-between items-center mt-16 fade-in-up" data-delay="1200">
                <button id="backButton"
                        class="glass-button px-8 py-4 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                    ← Back to Covenant
                </button>

                <button id="continueButton"
                        class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105 opacity-50 cursor-not-allowed"
                        disabled>
                    Complete Awakening →
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Eden Mode Step 4 Controller
class EdenModeStep4 {
    constructor() {
        this.selectedPath = null;
        this.tribalCallings = {};
        this.hasTribalCallings = false;
        this.formData = {
            businessName: '',
            businessType: '',
            businessDescription: '',
            selectedSela: null
        };

        this.init();
    }

    init() {
        this.loadPreviousSelections();
        this.initializeAnimations();
        this.setupPathSelection();
        this.setupFormValidation();
        this.setupNavigation();
        this.loadAvailableSelas();
    }

    loadPreviousSelections() {
        const selectedNation = sessionStorage.getItem('edenMode_selectedNation');
        const selectedTribe = sessionStorage.getItem('edenMode_selectedTribe');
        const fullName = sessionStorage.getItem('edenMode_fullName');
        const laborCategory = sessionStorage.getItem('edenMode_laborCategory');
        const ancestralGroup = sessionStorage.getItem('edenMode_ancestralGroup');

        this.displayJourneySummary(selectedNation, selectedTribe, fullName, laborCategory, ancestralGroup);
        this.loadTribalCallings(selectedNation);
    }

    displayJourneySummary(nation, tribe, name, labor, ancestralGroup) {
        const journeySummary = document.getElementById('journeySummary');

        const nationSymbols = {
            'JUDAH': '👑', 'BENJAMIN': '⚔️', 'LEVI': '📿',
            'EPHRAIM': '🌾', 'MANASSEH': '💼', 'REUBEN': '🌊',
            'SIMEON': '🎵', 'ZEBULUN': '🚢', 'ISSACHAR': '📊',
            'DAN': '⚖️', 'GAD': '🛡️', 'ASHER': '🍯', 'NAPHTALI': '🦌',
            'GERMANY': '🏰', 'AUSTRIA': '🎼', 'SWITZERLAND': '🏔️',
            'NETHERLANDS': '🌷', 'DENMARK': '⚓', 'NORWAY': '🏔️',
            'SWEDEN': '👑', 'FINLAND': '🌲', 'IRELAND': '🍀',
            'SCOTLAND': '🏴󠁧󠁢󠁳󠁣󠁴󠁿', 'BRITAIN': '🏴󠁧󠁢󠁥󠁮󠁧󠁿', 'FRANCE': '🗼',
            'RUSSIA': '🐻', 'POLAND': '🦅', 'CZECH': '💎',
            'SLOVAKIA': '🏰', 'BULGARIA': '🌹', 'ROMANIA': '🧛',
            'HUNGARY': '♨️', 'ITALY': '🎨', 'SPAIN': '🏰',
            'GREECE': '🏛️', 'ISRAEL': '🇮🇱', 'ARMENIA': '⛰️',
            'PERSIA': '👑', 'BABYLON': '🏛️', 'ARABIA': '🐪',
            'EGYPT': '🏺', 'ETHIOPIA': '🦁', 'CHINA': '🐉',
            'JAPAN': '🌸', 'KOREA': '🏔️', 'MONGOLIA': '🐎',
            'INDIA': '🕉️', 'ROME': '🦅'
        };

        const ancestralSymbols = {
            'Germanic Tribes': '⚔️', 'Nordic Peoples': '🏔️', 'Celtic Peoples': '🍀',
            'Celtic-Anglo-Saxon': '🏴󠁧󠁢󠁥󠁮󠁧󠁿', 'Celtic-Frankish': '⚜️', 'Slavic Peoples': '🐻',
            'Slavic-Turkic': '🏹', 'Daco-Roman': '🦅', 'Magyar Peoples': '🏇',
            'Italic Peoples': '🏛️', 'Iberian-Visigothic': '🏰', 'Hellenic Peoples': '🏺',
            'Abrahamic Covenant': '✡️', 'Armenian Peoples': '⛰️', 'Persian Peoples': '👑',
            'Mesopotamian Peoples': '📜', 'Semitic Peoples': '🐪', 'Hamitic Peoples': '🏺',
            'Cushite Peoples': '🦁', 'Sinic Peoples': '🐉', 'Yamato Peoples': '🌸',
            'Korean Peoples': '🏔️', 'Mongolic Peoples': '🐎', 'Indo-Aryan Peoples': '🕉️',
            'Roman Peoples': '🦅', 'Finno-Ugric Peoples': '🌲'
        };

        const laborIcons = {
            'spiritual': '📿', 'physical': '🔨', 'intellectual': '🧠',
            'service': '🤝', 'creative': '🎨', 'governance': '⚖️'
        };

        let heritageCard = '';
        if (ancestralGroup) {
            heritageCard = `
                <div class="glass-card-enhanced p-6 rounded-xl text-center">
                    <div class="text-4xl mb-3">${ancestralSymbols[ancestralGroup] || '🌍'}</div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-purple mb-2">Ancestry</h3>
                    <p class="text-text-secondary text-sm">${ancestralGroup}</p>
                </div>
            `;
        }

        journeySummary.innerHTML = `
            ${heritageCard}
            <div class="glass-card-enhanced p-6 rounded-xl text-center">
                <div class="text-4xl mb-3">${nationSymbols[nation] || '🏛️'}</div>
                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">Heritage</h3>
                <p class="text-text-secondary">${nation}${tribe ? ` (${tribe})` : ''}</p>
            </div>
            <div class="glass-card-enhanced p-6 rounded-xl text-center">
                <div class="text-4xl mb-3">👤</div>
                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">Identity</h3>
                <p class="text-text-secondary">${name || 'Covenant Member'}</p>
            </div>
            <div class="glass-card-enhanced p-6 rounded-xl text-center">
                <div class="text-4xl mb-3">${laborIcons[labor] || '🛠️'}</div>
                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">Calling</h3>
                <p class="text-text-secondary">${labor ? labor.charAt(0).toUpperCase() + labor.slice(1) : 'Labor'} Ministry</p>
            </div>
        `;
    }

    initializeAnimations() {
        const elements = document.querySelectorAll('.fade-in-up');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const delay = entry.target.dataset.delay || 0;
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, delay);
                }
            });
        }, { threshold: 0.1 });

        elements.forEach(element => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(40px)';
            element.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
            observer.observe(element);
        });
    }

    setupPathSelection() {
        const pathCards = document.querySelectorAll('.sela-path');

        pathCards.forEach(card => {
            card.addEventListener('click', () => {
                this.selectPath(card);
            });
        });

        // Switch between create and join
        const switchToCreate = document.getElementById('switchToCreate');
        if (switchToCreate) {
            switchToCreate.addEventListener('click', () => {
                this.selectPath(document.querySelector('[data-path="create"]'));
            });
        }
    }

    selectPath(selectedCard) {
        // Remove previous selections
        document.querySelectorAll('.sela-path').forEach(card => {
            card.classList.remove('selected');
            card.querySelector('.selection-indicator').style.opacity = '0';
        });

        // Select new path
        selectedCard.classList.add('selected');
        selectedCard.querySelector('.selection-indicator').style.opacity = '1';

        this.selectedPath = selectedCard.dataset.path;

        // Show appropriate form
        this.showPathForm(this.selectedPath);

        // Update continue button
        this.validateForm();
    }

    showPathForm(path) {
        const createForm = document.getElementById('createSelaForm');
        const joinOptions = document.getElementById('joinSelaOptions');
        const communityConfirmation = document.getElementById('communityMemberConfirmation');

        // Hide all forms first
        createForm.classList.add('hidden');
        joinOptions.classList.add('hidden');
        communityConfirmation.classList.add('hidden');

        // Show appropriate form
        if (path === 'create') {
            createForm.classList.remove('hidden');
        } else if (path === 'join') {
            joinOptions.classList.remove('hidden');
        } else if (path === 'community') {
            communityConfirmation.classList.remove('hidden');
        }

        // Scroll to form
        setTimeout(() => {
            let targetForm;
            if (path === 'create') targetForm = createForm;
            else if (path === 'join') targetForm = joinOptions;
            else if (path === 'community') targetForm = communityConfirmation;

            if (targetForm) {
                targetForm.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }, 300);
    }

    setupFormValidation() {
        const businessName = document.getElementById('businessName');
        const businessType = document.getElementById('businessType');
        const businessDescription = document.getElementById('businessDescription');

        if (businessName) {
            businessName.addEventListener('input', () => {
                this.formData.businessName = businessName.value.trim();
                this.validateForm();
            });
        }

        if (businessType) {
            businessType.addEventListener('change', () => {
                this.formData.businessType = businessType.value;
                this.validateTribalCalling(businessType.value);
                this.validateForm();
            });
        }

        if (businessDescription) {
            businessDescription.addEventListener('input', () => {
                this.formData.businessDescription = businessDescription.value.trim();
                this.validateForm();
            });
        }
    }

    validateForm() {
        const continueButton = document.getElementById('continueButton');
        let isValid = false;

        if (this.selectedPath === 'create') {
            isValid = this.formData.businessName.length >= 3 &&
                     this.formData.businessType &&
                     this.formData.businessDescription.length >= 20;
        } else if (this.selectedPath === 'join') {
            isValid = this.formData.selectedSela !== null;
        } else if (this.selectedPath === 'community') {
            // Community members don't need additional validation
            isValid = true;
        }

        if (isValid) {
            continueButton.disabled = false;
            continueButton.classList.remove('opacity-50', 'cursor-not-allowed');
            continueButton.classList.add('glow-on-hover');
        } else {
            continueButton.disabled = true;
            continueButton.classList.add('opacity-50', 'cursor-not-allowed');
            continueButton.classList.remove('glow-on-hover');
        }
    }

    async loadAvailableSelas() {
        try {
            const response = await fetch('/api/selas/available');
            const selas = await response.json();

            this.displayAvailableSelas(selas);
        } catch (error) {
            console.error('Error loading Selas:', error);
            this.displayAvailableSelas([]); // Show empty state
        }
    }

    displayAvailableSelas(selas) {
        const container = document.getElementById('availableSelas');

        if (selas.length === 0) {
            container.innerHTML = `
                <div class="col-span-full text-center py-12">
                    <div class="text-6xl mb-4">🏗️</div>
                    <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">
                        Be the First Pioneer
                    </h3>
                    <p class="text-text-secondary mb-6">
                        No Sela businesses exist yet. This is your opportunity to establish
                        the first covenant business and lead the community.
                    </p>
                    <button class="glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold"
                            onclick="document.querySelector('[data-path=\"create\"]').click()">
                        Create the First Sela
                    </button>
                </div>
            `;
            return;
        }

        container.innerHTML = selas.map(sela => `
            <div class="sela-option glass-card-enhanced p-6 rounded-xl cursor-pointer transition-all duration-300 hover:scale-105"
                 data-sela-id="${sela.id}">
                <div class="text-center mb-4">
                    <div class="text-3xl mb-3">${this.getBusinessIcon(sela.business_type)}</div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">
                        ${sela.business_name}
                    </h3>
                    <p class="text-cyber-purple text-sm">${sela.business_type}</p>
                </div>

                <p class="text-text-secondary text-sm mb-4 line-clamp-3">
                    ${sela.description}
                </p>

                <div class="space-y-2 text-xs">
                    <div class="flex justify-between">
                        <span class="text-text-tertiary">Workers:</span>
                        <span class="text-cyber-cyan">${sela.worker_count || 0}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-text-tertiary">Founded:</span>
                        <span class="text-cyber-cyan">${new Date(sela.created_at).getFullYear()}</span>
                    </div>
                </div>

                <div class="selection-indicator mt-4 text-center opacity-0 transition-opacity duration-300">
                    <div class="w-6 h-6 mx-auto rounded-full bg-cyber-green flex items-center justify-center">
                        <svg class="w-4 h-4 text-onyx-black" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <p class="text-cyber-green font-semibold text-xs mt-1">Selected</p>
                </div>
            </div>
        `).join('');

        // Setup sela selection
        container.querySelectorAll('.sela-option').forEach(option => {
            option.addEventListener('click', () => {
                this.selectSela(option);
            });
        });
    }

    getBusinessIcon(type) {
        const icons = {
            'spiritual': '📿', 'technology': '💻', 'agriculture': '🌾',
            'construction': '🏗️', 'education': '📚', 'healthcare': '🏥',
            'finance': '💰', 'creative': '🎨', 'trade': '🛒', 'manufacturing': '🏭'
        };
        return icons[type] || '🏢';
    }

    selectSela(selectedOption) {
        // Remove previous selections
        document.querySelectorAll('.sela-option').forEach(option => {
            option.classList.remove('selected');
            option.querySelector('.selection-indicator').style.opacity = '0';
        });

        // Select new sela
        selectedOption.classList.add('selected');
        selectedOption.querySelector('.selection-indicator').style.opacity = '1';

        this.formData.selectedSela = selectedOption.dataset.selaId;
        this.validateForm();
    }

    setupNavigation() {
        const backButton = document.getElementById('backButton');
        const continueButton = document.getElementById('continueButton');

        backButton.addEventListener('click', () => {
            window.location.href = '/auth/eden-mode/step3';
        });

        continueButton.addEventListener('click', () => {
            if (!continueButton.disabled) {
                this.storeFormData();

                continueButton.innerHTML = '🌟 Completing Awakening...';
                continueButton.disabled = true;

                setTimeout(() => {
                    window.location.href = '/auth/eden-mode/step5';
                }, 1500);
            }
        });
    }

    async loadTribalCallings(nationCode) {
        if (!nationCode) return;

        try {
            const response = await fetch(`/auth/eden-mode/api/tribal-callings/${nationCode}`);
            const data = await response.json();

            if (data.success) {
                this.tribalCallings = data.calling_categories;
                this.hasTribalCallings = data.total_callings > 0;
            }
        } catch (error) {
            console.error('Error loading tribal callings:', error);
            this.tribalCallings = {};
            this.hasTribalCallings = false;
        }
    }

    validateTribalCalling(businessType) {
        const notice = document.getElementById('tribalCallingNotice');
        const message = document.getElementById('tribalCallingMessage');

        if (!this.hasTribalCallings || !businessType) {
            notice.classList.add('hidden');
            return;
        }

        // Map business types to tribal calling categories
        const businessToCallingMap = {
            'governance': 'Leadership',
            'judicial': 'Judgment',
            'security': 'Security',
            'temporal': 'Timing',
            'spiritual': 'Spiritual'
        };

        const callingCategory = businessToCallingMap[businessType];

        if (callingCategory && this.tribalCallings[callingCategory]) {
            const callings = this.tribalCallings[callingCategory];
            const exclusiveCallings = callings.filter(c => c.is_exclusive);

            if (exclusiveCallings.length > 0) {
                notice.classList.remove('hidden');
                message.textContent = `This business category aligns with your tribal calling: ${exclusiveCallings[0].role_description}`;
                notice.className = notice.className.replace('bg-cyber-cyan/10 border-cyber-cyan/30', 'bg-cyber-green/10 border-cyber-green/30');
                notice.querySelector('.text-cyber-cyan').className = 'text-cyber-green text-lg';
                notice.querySelector('.text-cyber-cyan.font-semibold').className = 'text-cyber-green font-semibold text-sm';
            }
        } else if (Object.keys(this.tribalCallings).length > 0) {
            // Show available tribal callings
            const availableCategories = Object.keys(this.tribalCallings);
            notice.classList.remove('hidden');
            message.textContent = `Consider these categories aligned with your tribal calling: ${availableCategories.join(', ')}`;
            notice.className = notice.className.replace('bg-cyber-green/10 border-cyber-green/30', 'bg-cyber-cyan/10 border-cyber-cyan/30');
        }
    }

    storeFormData() {
        sessionStorage.setItem('edenMode_selaChoice', this.selectedPath);

        if (this.selectedPath === 'create') {
            sessionStorage.setItem('edenMode_businessName', this.formData.businessName);
            sessionStorage.setItem('edenMode_businessType', this.formData.businessType);
            sessionStorage.setItem('edenMode_businessDescription', this.formData.businessDescription);
        } else if (this.selectedPath === 'join') {
            sessionStorage.setItem('edenMode_selectedSela', this.formData.selectedSela);
        } else if (this.selectedPath === 'community') {
            // Community members don't need additional form data
            sessionStorage.setItem('edenMode_communityMember', 'true');
        }

        // Dispatch event for controller coordination
        document.dispatchEvent(new CustomEvent('selaChoiceMade', {
            detail: {
                choice: this.selectedPath,
                businessType: this.formData.businessType,
                businessName: this.formData.businessName,
                selectedSela: this.formData.selectedSela,
                communityMember: this.selectedPath === 'community'
            }
        }));
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new EdenModeStep4();
});
</script>

<style>
.glow-text {
    text-shadow: 0 0 20px rgba(154, 0, 255, 0.5);
}

.sela-path:hover, .sela-option:hover {
    transform: translateY(-4px);
    box-shadow: 0 15px 30px rgba(0, 255, 247, 0.2);
}

.sela-path.selected, .sela-option.selected {
    border: 2px solid var(--cyber-green);
    box-shadow: 0 0 25px rgba(0, 255, 136, 0.3);
    transform: translateY(-4px);
}

.eden-energy::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 30% 30%, rgba(154, 0, 255, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(0, 255, 136, 0.15) 0%, transparent 50%);
    animation: edenPulse 12s ease-in-out infinite;
}

@keyframes edenPulse {
    0%, 100% { opacity: 0.3; transform: scale(1) rotate(0deg); }
    50% { opacity: 0.8; transform: scale(1.1) rotate(180deg); }
}

.prosperity-streams::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(45deg, transparent 0%, rgba(0, 255, 136, 0.1) 50%, transparent 100%),
        linear-gradient(-45deg, transparent 0%, rgba(154, 0, 255, 0.1) 50%, transparent 100%);
    background-size: 150px 150px;
    animation: prosperityFlow 20s linear infinite;
}

@keyframes prosperityFlow {
    0% { transform: translateX(-150px) translateY(-150px); }
    100% { transform: translateX(150px) translateY(150px); }
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
{% endblock %}