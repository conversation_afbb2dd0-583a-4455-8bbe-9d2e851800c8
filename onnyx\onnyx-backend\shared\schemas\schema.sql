-- Onnyx Database Schema

-- Blockchain table
CREATE TABLE IF NOT EXISTS blocks (
    block_hash TEXT PRIMARY KEY,
    block_height INTEGER NOT NULL,
    previous_hash TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    difficulty INTEGER NOT NULL,
    nonce INTEGER NOT NULL,
    miner TEXT NOT NULL,
    transactions TEXT NOT NULL,  -- JSON array of transaction IDs
    merkle_root TEXT NOT NULL,
    size INTEGER NOT NULL,
    version TEXT NOT NULL,
    created_at INTEGER NOT NULL
);

-- Create index on block height
CREATE INDEX IF NOT EXISTS idx_blocks_height ON blocks(block_height);

-- Transactions table
CREATE TABLE IF NOT EXISTS transactions (
    tx_id TEXT PRIMARY KEY,
    block_hash TEXT,
    timestamp INTEGER NOT NULL,
    op TEXT NOT NULL,
    data TEXT NOT NULL,  -- J<PERSON><PERSON> object with transaction data
    sender TEXT NOT NULL,
    signature TEXT NOT NULL,
    status TEXT NOT NULL,
    created_at INTEGER NOT NULL,
    FOREIGN KEY (block_hash) REFERENCES blocks(block_hash)
);

-- Create index on transaction sender
CREATE INDEX IF NOT EXISTS idx_transactions_sender ON transactions(sender);

-- Mempool table
CREATE TABLE IF NOT EXISTS mempool (
    tx_id TEXT PRIMARY KEY,
    timestamp INTEGER NOT NULL,
    op TEXT NOT NULL,
    data TEXT NOT NULL,  -- JSON object with transaction data
    sender TEXT NOT NULL,
    signature TEXT NOT NULL,
    created_at INTEGER NOT NULL
);

-- Nations table
CREATE TABLE IF NOT EXISTS nations (
    nation_id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT NOT NULL,
    founder_id TEXT NOT NULL,
    metadata TEXT NOT NULL,  -- JSON object with nation metadata
    status TEXT NOT NULL,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (founder_id) REFERENCES identities(identity_id)
);

-- Biblical Nations table (Eden Mode - 47 nations for Genesis Covenant)
CREATE TABLE IF NOT EXISTS biblical_nations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    nation_code TEXT UNIQUE NOT NULL,
    nation_name TEXT NOT NULL,
    tribe_name TEXT NOT NULL,
    nation_type TEXT NOT NULL DEFAULT 'witness',
    description TEXT,
    flag_symbol TEXT DEFAULT '🛡️',
    biblical_reference TEXT,
    tribal_calling TEXT,
    governance_weight INTEGER DEFAULT 0,
    ancestral_group TEXT,
    ancestral_description TEXT,
    historical_connection TEXT
);

-- Tribal Calling Restrictions table (Genesis 49 based role restrictions)
CREATE TABLE IF NOT EXISTS tribal_calling_restrictions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    tribe_code TEXT NOT NULL,
    tribe_name TEXT NOT NULL,
    calling_category TEXT NOT NULL,
    role_name TEXT NOT NULL,
    role_description TEXT NOT NULL,
    biblical_reference TEXT NOT NULL,
    is_exclusive BOOLEAN DEFAULT TRUE,
    created_at TEXT,
    UNIQUE(tribe_code, role_name)
);

-- Create index on nation name
CREATE INDEX IF NOT EXISTS idx_nations_name ON nations(name);

-- Identities table (Updated for Eden Mode)
CREATE TABLE IF NOT EXISTS identities (
    identity_id TEXT PRIMARY KEY,
    id INTEGER UNIQUE,  -- For Eden Mode compatibility
    name TEXT NOT NULL,
    full_name TEXT DEFAULT '',  -- Eden Mode field
    email TEXT UNIQUE DEFAULT '',  -- Eden Mode field
    public_key TEXT NOT NULL,
    nation_id TEXT,
    nation_code TEXT,  -- Eden Mode field
    tribe_name TEXT,  -- Eden Mode field
    labor_category TEXT,  -- Eden Mode field
    wallet_address TEXT UNIQUE,  -- Eden Mode field
    cipp_tier INTEGER DEFAULT 0,  -- Eden Mode field
    covenant_accepted BOOLEAN DEFAULT FALSE,  -- Eden Mode field
    eden_mode_completed BOOLEAN DEFAULT FALSE,  -- Eden Mode field
    metadata TEXT NOT NULL,  -- JSON object with identity metadata
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (nation_id) REFERENCES nations(nation_id)
);

-- Create index on identity name and nation
CREATE INDEX IF NOT EXISTS idx_identities_name ON identities(name);
CREATE INDEX IF NOT EXISTS idx_identities_nation ON identities(nation_id);

-- Reputation table
CREATE TABLE IF NOT EXISTS reputation (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id TEXT NOT NULL,
    reputation_type TEXT NOT NULL,
    value INTEGER NOT NULL,
    issuer_id TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    tx_id TEXT NOT NULL,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    FOREIGN KEY (issuer_id) REFERENCES identities(identity_id),
    FOREIGN KEY (tx_id) REFERENCES transactions(tx_id)
);

-- Create index on reputation identity
CREATE INDEX IF NOT EXISTS idx_reputation_identity ON reputation(identity_id);

-- Badges table
CREATE TABLE IF NOT EXISTS badges (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id TEXT NOT NULL,
    badge_type TEXT NOT NULL,
    issuer_id TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    tx_id TEXT NOT NULL,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    FOREIGN KEY (issuer_id) REFERENCES identities(identity_id),
    FOREIGN KEY (tx_id) REFERENCES transactions(tx_id)
);

-- Create index on badges identity
CREATE INDEX IF NOT EXISTS idx_badges_identity ON badges(identity_id);

-- Tokens table
CREATE TABLE IF NOT EXISTS tokens (
    token_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    symbol TEXT NOT NULL,
    creator_id TEXT NOT NULL,
    supply INTEGER NOT NULL,
    category TEXT NOT NULL,
    decimals INTEGER NOT NULL,
    created_at INTEGER NOT NULL,
    metadata TEXT NOT NULL,  -- JSON object with token metadata
    FOREIGN KEY (creator_id) REFERENCES identities(identity_id)
);

-- Create index on token creator
CREATE INDEX IF NOT EXISTS idx_tokens_creator ON tokens(creator_id);

-- Token balances table
CREATE TABLE IF NOT EXISTS token_balances (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    token_id TEXT NOT NULL,
    identity_id TEXT NOT NULL,
    balance INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (token_id) REFERENCES tokens(token_id),
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    UNIQUE(token_id, identity_id)
);

-- Create index on token balances
CREATE INDEX IF NOT EXISTS idx_token_balances_token ON token_balances(token_id);
CREATE INDEX IF NOT EXISTS idx_token_balances_identity ON token_balances(identity_id);

-- Token transactions table
CREATE TABLE IF NOT EXISTS token_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    tx_id TEXT NOT NULL,
    token_id TEXT NOT NULL,
    from_id TEXT,
    to_id TEXT,
    amount INTEGER NOT NULL,
    operation TEXT NOT NULL,  -- MINT, SEND, BURN
    timestamp INTEGER NOT NULL,
    FOREIGN KEY (tx_id) REFERENCES transactions(tx_id),
    FOREIGN KEY (token_id) REFERENCES tokens(token_id),
    FOREIGN KEY (from_id) REFERENCES identities(identity_id),
    FOREIGN KEY (to_id) REFERENCES identities(identity_id)
);

-- Create index on token transactions
CREATE INDEX IF NOT EXISTS idx_token_transactions_token ON token_transactions(token_id);
CREATE INDEX IF NOT EXISTS idx_token_transactions_from ON token_transactions(from_id);
CREATE INDEX IF NOT EXISTS idx_token_transactions_to ON token_transactions(to_id);

-- Sela business registry table
CREATE TABLE IF NOT EXISTS selas (
    sela_id TEXT PRIMARY KEY,
    identity_id TEXT NOT NULL,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    stake_amount INTEGER NOT NULL,
    stake_token_id TEXT NOT NULL,
    created_at INTEGER NOT NULL,
    metadata TEXT NOT NULL,  -- JSON object with Sela metadata
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    FOREIGN KEY (stake_token_id) REFERENCES tokens(token_id)
);

-- Create index on Sela identity
CREATE INDEX IF NOT EXISTS idx_selas_identity ON selas(identity_id);

-- Zeman time/work credits table
CREATE TABLE IF NOT EXISTS zeman_credits (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id TEXT NOT NULL,
    hours REAL NOT NULL,
    description TEXT NOT NULL,
    issuer_id TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    tx_id TEXT NOT NULL,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    FOREIGN KEY (issuer_id) REFERENCES identities(identity_id),
    FOREIGN KEY (tx_id) REFERENCES transactions(tx_id)
);

-- Create index on Zeman credits identity
CREATE INDEX IF NOT EXISTS idx_zeman_credits_identity ON zeman_credits(identity_id);

-- Etzem trust scores table
CREATE TABLE IF NOT EXISTS etzem_scores (
    identity_id TEXT PRIMARY KEY,
    composite_score INTEGER NOT NULL,
    zeman_score INTEGER NOT NULL,
    reputation_score INTEGER NOT NULL,
    sela_score INTEGER NOT NULL,
    token_activity_score INTEGER NOT NULL,
    governance_score INTEGER NOT NULL,
    longevity_score INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Voice Scrolls (governance proposals) table
CREATE TABLE IF NOT EXISTS voice_scrolls (
    scroll_id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    proposer_id TEXT NOT NULL,
    status TEXT NOT NULL,  -- PROPOSED, VOTING, PASSED, REJECTED, IMPLEMENTED
    proposal_type TEXT NOT NULL,
    params TEXT NOT NULL,  -- JSON object with proposal parameters
    voting_start INTEGER NOT NULL,
    voting_end INTEGER NOT NULL,
    implementation_time INTEGER,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (proposer_id) REFERENCES identities(identity_id)
);

-- Create index on Voice Scrolls status
CREATE INDEX IF NOT EXISTS idx_voice_scrolls_status ON voice_scrolls(status);

-- Voice Scroll votes table
CREATE TABLE IF NOT EXISTS scroll_votes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    scroll_id TEXT NOT NULL,
    voter_id TEXT NOT NULL,
    vote TEXT NOT NULL,  -- YES, NO, ABSTAIN
    weight INTEGER NOT NULL,
    timestamp INTEGER NOT NULL,
    tx_id TEXT NOT NULL,
    FOREIGN KEY (scroll_id) REFERENCES voice_scrolls(scroll_id),
    FOREIGN KEY (voter_id) REFERENCES identities(identity_id),
    FOREIGN KEY (tx_id) REFERENCES transactions(tx_id),
    UNIQUE(scroll_id, voter_id)
);

-- Create index on Voice Scroll votes
CREATE INDEX IF NOT EXISTS idx_scroll_votes_scroll ON scroll_votes(scroll_id);

-- Event logs table
CREATE TABLE IF NOT EXISTS event_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    event_type TEXT NOT NULL,
    entity_id TEXT NOT NULL,
    data TEXT NOT NULL,  -- JSON object with event data
    timestamp INTEGER NOT NULL
);

-- Create index on event logs
CREATE INDEX IF NOT EXISTS idx_event_logs_type ON event_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_event_logs_entity ON event_logs(entity_id);
CREATE INDEX IF NOT EXISTS idx_event_logs_timestamp ON event_logs(timestamp);

-- Activity ledger table
CREATE TABLE IF NOT EXISTS activity_ledger (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id TEXT NOT NULL,
    activity_type TEXT NOT NULL,
    data TEXT NOT NULL,  -- JSON object with activity data
    timestamp INTEGER NOT NULL,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Create index on activity ledger
CREATE INDEX IF NOT EXISTS idx_activity_ledger_identity ON activity_ledger(identity_id);
CREATE INDEX IF NOT EXISTS idx_activity_ledger_timestamp ON activity_ledger(timestamp);

-- Validator rotation registry table
CREATE TABLE IF NOT EXISTS rotation_registry (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sela_id TEXT NOT NULL,
    identity_id TEXT NOT NULL,
    etzem_score INTEGER NOT NULL,
    eligible BOOLEAN NOT NULL,
    last_block_mined INTEGER,
    next_rotation_time INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (sela_id) REFERENCES selas(sela_id),
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Create index on rotation registry
CREATE INDEX IF NOT EXISTS idx_rotation_registry_eligible ON rotation_registry(eligible);
CREATE INDEX IF NOT EXISTS idx_rotation_registry_next_rotation ON rotation_registry(next_rotation_time);

-- Chain parameters table
CREATE TABLE IF NOT EXISTS chain_parameters (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    default_value TEXT NOT NULL,
    description TEXT,
    category TEXT,
    last_updated INTEGER,
    last_updated_by TEXT,
    metadata TEXT,
    FOREIGN KEY (last_updated_by) REFERENCES identities(identity_id)
);

-- Create index on chain parameters category
CREATE INDEX IF NOT EXISTS idx_chain_parameters_category ON chain_parameters(category);

-- Rotation table
CREATE TABLE IF NOT EXISTS rotation (
    id TEXT PRIMARY KEY,
    current_validator TEXT,
    queue TEXT,
    update_interval INTEGER,
    min_etzem_score INTEGER,
    required_badges TEXT,
    last_updated INTEGER,
    FOREIGN KEY (current_validator) REFERENCES selas(sela_id)
);

-- Create index on rotation last updated
CREATE INDEX IF NOT EXISTS idx_rotation_last_updated ON rotation(last_updated);

-- Eden Mode and Biblical Tokenomics Tables

-- Sela Relationships Table (Eden Mode)
CREATE TABLE IF NOT EXISTS sela_relationships (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sela_id INTEGER NOT NULL,
    identity_id INTEGER NOT NULL,
    relationship_type TEXT NOT NULL,
    created_at TEXT,
    FOREIGN KEY (sela_id) REFERENCES selas(sela_id),
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Covenant Transactions Table (Eden Mode)
CREATE TABLE IF NOT EXISTS covenant_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id INTEGER NOT NULL,
    transaction_type TEXT NOT NULL,
    transaction_data TEXT,
    block_number INTEGER,
    created_at TEXT,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Etzem Tokens Table (Biblical Tokenomics)
CREATE TABLE IF NOT EXISTS etzem_tokens (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id INTEGER NOT NULL,
    amount DECIMAL(18,8) NOT NULL,
    transaction_type TEXT NOT NULL,
    reason TEXT,
    created_at TEXT,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Gleaning Pool Table (Biblical Tokenomics)
CREATE TABLE IF NOT EXISTS gleaning_pool (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id INTEGER NOT NULL,
    contribution_amount DECIMAL(18,8) NOT NULL,
    distribution_amount DECIMAL(18,8) DEFAULT 0,
    pool_cycle INTEGER NOT NULL,
    created_at TEXT,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Sabbath Enforcement Table (Biblical Tokenomics)
CREATE TABLE IF NOT EXISTS sabbath_enforcement (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id INTEGER NOT NULL,
    sabbath_date TEXT NOT NULL,
    compliance_status TEXT DEFAULT 'pending',
    penalty_amount DECIMAL(18,8) DEFAULT 0,
    created_at TEXT,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Yovel Cycle Table (Biblical Tokenomics)
CREATE TABLE IF NOT EXISTS yovel_cycles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    cycle_number INTEGER NOT NULL,
    start_date TEXT NOT NULL,
    end_date TEXT,
    reset_completed BOOLEAN DEFAULT FALSE,
    created_at TEXT
);

-- Mining Rewards Table (Biblical Tokenomics)
CREATE TABLE IF NOT EXISTS mining_rewards (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id INTEGER NOT NULL,
    reward_amount DECIMAL(18,8) NOT NULL,
    tier_level INTEGER NOT NULL,
    block_number INTEGER,
    created_at TEXT,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Labor Records Table (Covenant Labor System)
CREATE TABLE IF NOT EXISTS labor_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id INTEGER NOT NULL,
    sela_id INTEGER,
    labor_type TEXT NOT NULL,
    hours_worked DECIMAL(8,2) NOT NULL,
    contribution_value DECIMAL(18,8) NOT NULL,
    verification_status TEXT DEFAULT 'pending',
    created_at TEXT,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    FOREIGN KEY (sela_id) REFERENCES selas(sela_id)
);

-- CIPP (Covenant Identity Protection Protocol) Table
CREATE TABLE IF NOT EXISTS cipp_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id INTEGER NOT NULL,
    verification_tier INTEGER NOT NULL DEFAULT 0,
    etzem_score DECIMAL(8,2) DEFAULT 0,
    protection_status TEXT DEFAULT 'active',
    vault_frozen BOOLEAN DEFAULT FALSE,
    exile_mode BOOLEAN DEFAULT FALSE,
    created_at TEXT,
    updated_at TEXT,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Additional indexes for Eden Mode and Biblical Tokenomics
CREATE INDEX IF NOT EXISTS idx_identities_email ON identities(email);
CREATE INDEX IF NOT EXISTS idx_identities_wallet ON identities(wallet_address);
CREATE INDEX IF NOT EXISTS idx_identities_nation_code ON identities(nation_code);
CREATE INDEX IF NOT EXISTS idx_sela_relationships_sela ON sela_relationships(sela_id);
CREATE INDEX IF NOT EXISTS idx_sela_relationships_identity ON sela_relationships(identity_id);
CREATE INDEX IF NOT EXISTS idx_covenant_transactions_identity ON covenant_transactions(identity_id);
CREATE INDEX IF NOT EXISTS idx_etzem_tokens_identity ON etzem_tokens(identity_id);
CREATE INDEX IF NOT EXISTS idx_labor_records_identity ON labor_records(identity_id);
CREATE INDEX IF NOT EXISTS idx_labor_records_sela ON labor_records(sela_id);
CREATE INDEX IF NOT EXISTS idx_cipp_records_identity ON cipp_records(identity_id);
