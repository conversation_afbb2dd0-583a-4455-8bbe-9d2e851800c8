"""
Eden Mode Routes - Immersive Identity Onboarding Experience
Handles the spiritual awakening journey through biblical covenant registration
"""

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from shared.db.db import db
from blockchain.tokenomics.biblical_tokenomics import BiblicalTokenomics
import hashlib
import secrets
import time
import json
from datetime import datetime

eden_mode_bp = Blueprint('eden_mode', __name__, url_prefix='/auth/eden-mode')

@eden_mode_bp.route('/step1')
def step1():
    """The Forgotten Legacy - Introduction to covenant awakening"""
    return render_template('auth/eden_mode_step1.html')

@eden_mode_bp.route('/step2')
def step2():
    """Nations Hidden in Plain Sight - Tribal/nation selection with ancestral heritage"""
    # Load biblical nations for selection - organized by type with ancestral data
    biblical_nations = db.query("""
        SELECT nation_code, nation_name, tribe_name, nation_type, description, flag_symbol,
               ancestral_group, ancestral_description, historical_connection
        FROM biblical_nations
        ORDER BY nation_type DESC, nation_name
    """)

    # Get unique ancestral groups for witness nations
    ancestral_groups = db.query("""
        SELECT DISTINCT ancestral_group, COUNT(*) as nation_count
        FROM biblical_nations
        WHERE nation_type = 'witness' AND ancestral_group IS NOT NULL
        GROUP BY ancestral_group
        ORDER BY ancestral_group
    """)

    return render_template('auth/eden_mode_step2.html',
                         biblical_nations=biblical_nations,
                         ancestral_groups=ancestral_groups)

@eden_mode_bp.route('/step3')
def step3():
    """The Covenant Reawakens - Role and labor identification with tribal calling restrictions"""
    # Get selected nation/tribe from session or query params
    selected_nation = request.args.get('nation') or session.get('edenMode_selectedNation')
    selected_tribe = request.args.get('tribe') or session.get('edenMode_selectedTribe')

    # Load tribal calling restrictions if a covenant tribe is selected
    tribal_callings = []
    if selected_tribe and selected_nation:
        # Check if this is a covenant tribe with specific callings
        tribal_callings = db.query("""
            SELECT calling_category, role_name, role_description, biblical_reference, is_exclusive
            FROM tribal_calling_restrictions
            WHERE tribe_code = ? OR tribe_name = ?
            ORDER BY calling_category, role_name
        """, [selected_nation.upper(), selected_tribe])

    return render_template('auth/eden_mode_step3.html',
                         selected_nation=selected_nation,
                         selected_tribe=selected_tribe,
                         tribal_callings=tribal_callings)

@eden_mode_bp.route('/step4')
def step4():
    """Eden Mode Activation - Business/Sela creation or joining"""
    return render_template('auth/eden_mode_step4.html')

@eden_mode_bp.route('/step5')
def step5():
    """Blockchain Inscription - Final identity creation"""
    return render_template('auth/eden_mode_step5.html')

@eden_mode_bp.route('/api/selas/available')
def get_available_selas():
    """Get list of available Sela businesses to join"""
    try:
        selas = db.query("""
            SELECT s.id, s.business_name, s.business_type, s.description, s.created_at,
                   COUNT(sr.id) as worker_count
            FROM selas s
            LEFT JOIN sela_relationships sr ON s.id = sr.sela_id AND sr.relationship_type = 'worker'
            WHERE s.status = 'active'
            GROUP BY s.id, s.business_name, s.business_type, s.description, s.created_at
            ORDER BY s.created_at DESC
        """)

        return jsonify(selas)
    except Exception as e:
        print(f"Error loading Selas: {e}")
        return jsonify([])

@eden_mode_bp.route('/create-identity', methods=['POST'])
def create_identity():
    """Create new covenant identity with Eden Mode data"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['fullName', 'email', 'selectedNation', 'laborCategory', 'selaChoice']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        # Generate cryptographic identity
        private_key = secrets.token_hex(32)
        public_key = hashlib.sha256(private_key.encode()).hexdigest()
        wallet_address = f"ONX{hashlib.sha256(public_key.encode()).hexdigest()[:34]}"

        # Generate unique identity ID
        identity_data = f"{data['fullName']}_{data['email']}_{int(time.time())}"
        identity_id = f"ONX{hashlib.sha256(identity_data.encode()).hexdigest()[:16]}"

        # Create identity record (using production schema column names)
        db.insert('identities', {
            'identity_id': identity_id,
            'name': data['fullName'],  # Use 'name' instead of 'full_name'
            'email': data['email'],
            'public_key': public_key,
            'nation_id': data['selectedNation'],  # Map to nation_id
            'nation_code': data['selectedNation'],
            'nation_name': data.get('selectedNation', 'Unknown'),
            'metadata': json.dumps({
                'tribe_name': data.get('selectedTribe'),
                'labor_category': data['laborCategory'],
                'wallet_address': wallet_address,
                'eden_mode_completed': True,
                'registration_source': 'eden_mode'
            }),
            'status': 'active',
            'created_at': int(time.time()),
            'updated_at': int(time.time()),
            'covenant_accepted': True,
            'verification_level': 1  # Verified through Eden Mode
        })

        # Handle Sela choice
        sela_id = None
        if data['selaChoice'] == 'create':
            # Create new Sela business
            sela_id = create_new_sela(identity_id, data)
        elif data['selaChoice'] == 'join' and data.get('selectedSela'):
            # Join existing Sela
            sela_id = join_existing_sela(identity_id, data['selectedSela'])
        elif data['selaChoice'] == 'community':
            # Community members don't need Sela association
            sela_id = None

        # Initialize biblical tokenomics
        initialize_biblical_tokenomics(identity_id, data)

        # Create blockchain transaction (simulated)
        block_number = create_covenant_transaction(identity_id, data)

        # Create session
        session['identity_id'] = identity_id
        session['wallet_address'] = wallet_address
        session['covenant_member'] = True

        return jsonify({
            'success': True,
            'identity_id': identity_id,
            'wallet_address': wallet_address,
            'sela_id': sela_id,
            'block_number': block_number,
            'message': 'Covenant identity successfully created',
            'redirect_url': '/dashboard/'
        })

    except Exception as e:
        print(f"Error creating identity: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

def create_new_sela(identity_id, data):
    """Create a new Sela business validator"""
    try:
        # Generate unique sela ID
        sela_data = f"{data['businessName']}_{identity_id}_{int(time.time())}"
        sela_id = f"SELA{hashlib.sha256(sela_data.encode()).hexdigest()[:16]}"

        # Create Sela record (using production schema column names)
        db.insert('selas', {
            'sela_id': sela_id,
            'identity_id': identity_id,
            'name': data['businessName'],  # Use 'name' instead of 'business_name'
            'category': data['businessType'],  # Use 'category' instead of 'business_type'
            'description': data['businessDescription'],
            'status': 'active',
            'created_at': int(time.time()),
            'updated_at': int(time.time()),
            'metadata': json.dumps({
                'is_validator': True,
                'accepting_members': True,
                'registration_source': 'eden_mode'
            })
        })

        # Note: In production schema, ownership is tracked via identity_id in selas table
        # No separate sela_relationships table needed

        # Initialize Sela in biblical tokenomics
        bt = BiblicalTokenomics(db)
        bt.register_sela_validator(sela_id, data['businessName'])

        return sela_id

    except Exception as e:
        print(f"Error creating Sela: {e}")
        raise

def join_existing_sela(identity_id, sela_id):
    """Join an existing Sela business"""
    try:
        # Verify Sela exists and is accepting members
        sela = db.query_one("""
            SELECT sela_id, name
            FROM selas
            WHERE sela_id = ? AND status = 'active'
        """, [sela_id])

        if not sela:
            raise Exception("Sela not found or inactive")

        # Note: Removed accepting_members check since column may not exist

        # Note: In production schema, joining a Sela would be tracked via labor_records table
        # For now, we'll just return the sela_id to indicate successful joining
        # The actual relationship will be established when the user creates labor records

        return sela_id

    except Exception as e:
        print(f"Error joining Sela: {e}")
        raise

def initialize_biblical_tokenomics(identity_id, data):
    """Initialize biblical tokenomics for new identity"""
    try:
        bt = BiblicalTokenomics(db)

        # Register identity in biblical tokenomics
        bt.register_covenant_member(
            identity_id=identity_id,
            nation_code=data['selectedNation'],
            labor_category=data['laborCategory']
        )

        # Grant initial Etzem tokens for covenant completion
        bt.award_etzem_tokens(
            identity_id=identity_id,
            amount=100,  # Welcome bonus
            reason="Eden Mode covenant completion",
            transaction_type="covenant_bonus"
        )

        # Initialize gleaning pool participation
        bt.initialize_gleaning_participation(identity_id)

        # Set up Sabbath enforcement
        bt.enable_sabbath_enforcement(identity_id)

    except Exception as e:
        print(f"Error initializing biblical tokenomics: {e}")
        raise

def create_covenant_transaction(identity_id, data):
    """Create blockchain transaction for covenant identity (simulated)"""
    try:
        # In a real implementation, this would create an actual blockchain transaction
        # For now, we'll simulate it by creating a record

        transaction_data = {
            'type': 'COVENANT_IDENTITY_CREATE',
            'identity_id': identity_id,
            'nation_code': data['selectedNation'],
            'labor_category': data['laborCategory'],
            'sela_choice': data['selaChoice'],
            'timestamp': int(time.time()),
            'covenant_hash': hashlib.sha256(f"{identity_id}{data['fullName']}{data['selectedNation']}".encode()).hexdigest()
        }

        # Simulate block creation
        try:
            block_count = db.query_one("SELECT COUNT(*) as count FROM blocks")
            block_number = (block_count['count'] if block_count else 0) + 1
        except:
            block_number = 1

        # Store transaction record in main transactions table (production schema)
        tx_data = f"{identity_id}{int(time.time())}"
        tx_id = f"TX{hashlib.sha256(tx_data.encode()).hexdigest()[:16]}"
        db.insert('transactions', {
            'tx_id': tx_id,
            'timestamp': int(time.time()),
            'op': 'COVENANT_IDENTITY_CREATE',
            'data': json.dumps(transaction_data),
            'sender': identity_id,
            'signature': hashlib.sha256(f"{tx_id}{identity_id}".encode()).hexdigest(),
            'status': 'confirmed',
            'created_at': int(time.time())
        })

        return block_number

    except Exception as e:
        print(f"Error creating covenant transaction: {e}")
        raise

@eden_mode_bp.route('/validate-nation', methods=['POST'])
def validate_nation():
    """Validate selected nation and return additional information"""
    try:
        data = request.get_json()
        nation_code = data.get('nation_code')

        if not nation_code:
            return jsonify({'success': False, 'error': 'Nation code required'}), 400

        nation = db.query_one("""
            SELECT nation_code, nation_name, tribe_name, nation_type, description, flag_symbol
            FROM biblical_nations
            WHERE nation_code = ?
        """, [nation_code])

        if not nation:
            return jsonify({'success': False, 'error': 'Nation not found'}), 404

        return jsonify({
            'success': True,
            'nation': nation
        })

    except Exception as e:
        print(f"Error validating nation: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@eden_mode_bp.route('/api/tribal-callings/<tribe_code>')
def get_tribal_callings(tribe_code):
    """Get available tribal callings for a specific tribe"""
    try:
        # Get tribal calling restrictions
        callings = db.query("""
            SELECT calling_category, role_name, role_description, biblical_reference, is_exclusive
            FROM tribal_calling_restrictions
            WHERE tribe_code = ? OR tribe_name = ?
            ORDER BY calling_category, role_name
        """, [tribe_code.upper(), tribe_code.title()])

        # Group by category
        calling_categories = {}
        for calling in callings:
            category = calling['calling_category']
            if category not in calling_categories:
                calling_categories[category] = []
            calling_categories[category].append(calling)

        return jsonify({
            'success': True,
            'tribe_code': tribe_code.upper(),
            'calling_categories': calling_categories,
            'total_callings': len(callings)
        })

    except Exception as e:
        print(f"Error getting tribal callings: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@eden_mode_bp.route('/api/validate-role-selection', methods=['POST'])
def validate_role_selection():
    """Validate if a user can select a specific role based on their tribal calling"""
    try:
        data = request.get_json()
        tribe_code = data.get('tribe_code', '').upper()
        selected_role = data.get('selected_role', '')

        if not tribe_code or not selected_role:
            return jsonify({'success': False, 'error': 'Tribe code and role required'}), 400

        # Check if this role is restricted to this tribe
        restriction = db.query_one("""
            SELECT role_name, role_description, biblical_reference, is_exclusive
            FROM tribal_calling_restrictions
            WHERE (tribe_code = ? OR tribe_name = ?) AND role_name = ?
        """, [tribe_code, tribe_code.title(), selected_role])

        if restriction:
            # Role is available to this tribe
            return jsonify({
                'success': True,
                'allowed': True,
                'role_info': restriction,
                'message': f'Role "{selected_role}" is available to the tribe of {tribe_code.title()}'
            })
        else:
            # Check if this role is exclusive to another tribe
            other_restriction = db.query_one("""
                SELECT tribe_name, biblical_reference
                FROM tribal_calling_restrictions
                WHERE role_name = ? AND is_exclusive = 1
            """, [selected_role])

            if other_restriction:
                return jsonify({
                    'success': True,
                    'allowed': False,
                    'message': f'Role "{selected_role}" is exclusively reserved for the tribe of {other_restriction["tribe_name"]}',
                    'biblical_reference': other_restriction['biblical_reference']
                })
            else:
                # Role is not restricted, available to all
                return jsonify({
                    'success': True,
                    'allowed': True,
                    'message': f'Role "{selected_role}" is available to all covenant members'
                })

    except Exception as e:
        print(f"Error validating role selection: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@eden_mode_bp.route('/api/ancestral-groups')
def get_ancestral_groups():
    """Get all ancestral groups for witness nations"""
    try:
        ancestral_groups = db.query("""
            SELECT ancestral_group, COUNT(*) as nation_count,
                   GROUP_CONCAT(nation_name, ', ') as nations
            FROM biblical_nations
            WHERE nation_type = 'witness' AND ancestral_group IS NOT NULL
            GROUP BY ancestral_group
            ORDER BY ancestral_group
        """)

        return jsonify({
            'success': True,
            'ancestral_groups': ancestral_groups,
            'total_groups': len(ancestral_groups)
        })

    except Exception as e:
        print(f"Error getting ancestral groups: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@eden_mode_bp.route('/api/nations-by-ancestry/<ancestral_group>')
def get_nations_by_ancestry(ancestral_group):
    """Get nations filtered by ancestral group"""
    try:
        nations = db.query("""
            SELECT nation_code, nation_name, tribe_name, description, flag_symbol,
                   ancestral_group, ancestral_description, historical_connection
            FROM biblical_nations
            WHERE nation_type = 'witness' AND ancestral_group = ?
            ORDER BY nation_name
        """, [ancestral_group])

        return jsonify({
            'success': True,
            'ancestral_group': ancestral_group,
            'nations': nations,
            'nation_count': len(nations)
        })

    except Exception as e:
        print(f"Error getting nations by ancestry: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@eden_mode_bp.route('/check-email', methods=['POST'])
def check_email():
    """Check if email is already registered"""
    try:
        data = request.get_json()
        email = data.get('email')

        if not email:
            return jsonify({'success': False, 'error': 'Email required'}), 400

        existing = db.query_one("""
            SELECT id FROM identities WHERE email = ?
        """, [email])

        return jsonify({
            'success': True,
            'available': existing is None
        })

    except Exception as e:
        print(f"Error checking email: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# Error handlers
@eden_mode_bp.errorhandler(404)
def not_found(error):
    return redirect(url_for('eden_mode.step1'))

@eden_mode_bp.errorhandler(500)
def internal_error(error):
    return jsonify({'success': False, 'error': 'Internal server error'}), 500
