{% extends "base.html" %}

{% block title %}Access Portal - ONNYX Platform{% endblock %}

{% block content %}
<div class="min-h-screen hero-gradient cyber-grid relative flex items-center justify-center py-20">
    <!-- Floating particles -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-cyan rounded-full animate-ping"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-purple rounded-full animate-pulse"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce"></div>
    </div>

    <div class="container-sm relative z-10 w-full max-w-md mx-auto">
        <div class="card">
            <!-- Enhanced Header -->
            <div class="card-header text-center">
                <div class="flex items-center justify-center mx-auto mb-6">
                    <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                         alt="ONNYX Logo"
                         class="onnyx-page-logo w-16 h-16 md:w-20 md:h-20 object-contain"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <!-- Fallback symbol only if image fails -->
                    <span class="text-6xl font-black text-cyber-cyan" style="display: none;">⬢</span>
                </div>
                <h1 class="card-title text-3xl">
                    <span class="hologram-text">Access Portal</span>
                </h1>
                <p class="card-subtitle text-lg">
                    Enter your verified identity credentials
                </p>
            </div>

            <!-- Enhanced Login Form -->
            <div class="card-body">
                <form method="POST" class="space-y-6">
                    <div class="form-group">
                        <label for="email" class="form-label required">
                            Email Address
                        </label>
                        <div class="relative">
                            <input type="email"
                                   id="email"
                                   name="email"
                                   required
                                   class="form-control pl-12"
                                   placeholder="Enter your registered email">
                            <svg class="w-5 h-5 text-cyber-cyan absolute left-4 top-1/2 transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- Security Notice -->
                    <div class="card border border-cyber-cyan/30 bg-cyber-cyan/5">
                        <div class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-cyber-cyan mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.012-3.016A9.956 9.956 0 0121 12c0 1.657-.45 3.212-1.237 4.54M16.5 17a9.956 9.956 0 01-4.5 1c-1.657 0-3.212-.45-4.54-1.237M3 12a9.956 9.956 0 011-4.54m5.5-2.5a9.956 9.956 0 014.5-1c1.657 0 3.212.45 4.54 1.237"></path>
                            </svg>
                            <div>
                                <p class="text-sm text-cyber-cyan font-medium">Secure Authentication</p>
                                <p class="text-xs text-gray-400 mt-1">
                                    Your identity is verified using quantum-resistant cryptography
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Submit Button -->
                    <button type="submit" class="btn btn-primary btn-lg w-full">
                        <span class="text-xl">🚀</span>
                        <span>Access Network</span>
                    </button>
                </form>
            </div>

            <!-- Enhanced Footer -->
            <div class="card-footer">
                <div class="text-center w-full">
                    <div class="relative mb-6">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-white/10"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-4 bg-onyx-black text-gray-400">New to ONNYX?</span>
                        </div>
                    </div>

                    <div class="flex flex-col gap-3 mb-6">
                        <a href="{{ url_for('register_choice') }}" class="btn btn-secondary">
                            <span class="text-lg">🔐</span>
                            <span>Create Identity</span>
                        </a>
                        <a href="{{ url_for('index') }}" class="btn btn-ghost btn-sm">
                            ← Back to Home
                        </a>
                    </div>

                    <!-- Enhanced Features List -->
                    <div class="pt-6 border-t border-white/10">
                        <h3 class="text-sm font-orbitron font-semibold text-cyber-cyan mb-4">Platform Features</h3>
                        <ul class="space-y-2 text-sm text-gray-400">
                            <li class="flex items-center justify-center space-x-2">
                                <div class="w-1.5 h-1.5 bg-cyber-cyan rounded-full"></div>
                                <span>Quantum-resistant security</span>
                            </li>
                            <li class="flex items-center justify-center space-x-2">
                                <div class="w-1.5 h-1.5 bg-cyber-purple rounded-full"></div>
                                <span>Decentralized validation</span>
                            </li>
                            <li class="flex items-center justify-center space-x-2">
                                <div class="w-1.5 h-1.5 bg-cyber-blue rounded-full"></div>
                                <span>Trusted business network</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation and enhancement
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const emailInput = document.getElementById('email');
    const submitButton = form.querySelector('button[type="submit"]');

    // Email validation
    emailInput.addEventListener('input', function() {
        const email = this.value.trim();
        const isValid = email && email.includes('@') && email.includes('.');

        if (isValid) {
            this.classList.remove('border-red-500/50');
            this.classList.add('border-cyber-cyan/50');
        } else if (email) {
            this.classList.remove('border-cyber-cyan/50');
            this.classList.add('border-red-500/50');
        }
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        const email = emailInput.value.trim();

        if (!email || !email.includes('@')) {
            e.preventDefault();
            Onnyx.utils.showNotification('Please enter a valid email address', 'error');
            emailInput.focus();
            return;
        }

        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<div class="spinner inline-block mr-2"></div>Authenticating...';
    });

    // Auto-focus email field
    emailInput.focus();
});
</script>
{% endblock %}
